from aqt import mw
from aqt.qt import *
from aqt.utils import showInfo
from anki.notes import Note
import json
from ..lang import get_text
from .LinkDialog import LinkDialog

class CardLinker:

    def __init__(self):
        self.linked_cards_field = 'LinkedCards'

    def resolve_link_to_card(self, link):
        """Resolve a stored link (which may contain legacy card_id or GUID+ord) to a current Card object.
        Returns the Card or None if not found.
        """
        try:
            # 1) Try legacy numeric card_id directly
            cid = link.get('card_id') if isinstance(link, dict) else None
            if cid:
                try:
                    card = mw.col.getCard(cid)
                    if card:
                        return card
                except:
                    pass
            # 2) Try via note GUID + card ord (stable across imports)
            guid = link.get('note_guid') if isinstance(link, dict) else None
            ord_val = link.get('card_ord') if isinstance(link, dict) else None
            if guid is not None and ord_val is not None:
                try:
                    nid = mw.col.db.scalar("select id from notes where guid=?", guid)
                    if nid:
                        cid2 = mw.col.db.scalar("select id from cards where nid=? and ord=?", nid, ord_val)
                        if cid2:
                            return mw.col.getCard(cid2)
                except:
                    pass
            # 3) Fallback: note_id + ord (if present)
            nid2 = link.get('note_id') if isinstance(link, dict) else None
            if nid2 is not None and ord_val is not None:
                try:
                    cid3 = mw.col.db.scalar("select id from cards where nid=? and ord=?", nid2, ord_val)
                    if cid3:
                        return mw.col.getCard(cid3)
                except:
                    pass
        except:
            pass
        return None

    def link_matches_card(self, link, card):
        """Return True if the stored link refers to the given card, using card_id or GUID+ord equivalence."""
        try:
            if not card:
                return False
            # Direct id match
            if isinstance(link, dict) and link.get('card_id') == card.id:
                return True
            # GUID + ord match
            try:
                note = card.note()
                guid = getattr(note, 'guid', None)
                ord_val = getattr(card, 'ord', None)
                if guid is not None and ord_val is not None:
                    if link.get('note_guid') == guid and link.get('card_ord') == ord_val:
                        return True
                # Fallback: note_id + ord
                if link.get('note_id') == note.id and link.get('card_ord') == ord_val:
                    return True
            except:
                pass
        except:
            pass
        return False

    def build_link_info(self, card, link_text):
        """Construct the link info dict including stable identifiers."""
        try:
            linked_note = card.note()
            deck_name = mw.col.decks.name(card.did)
            info = {
                'title': link_text,
                'deck': deck_name,
                # stable identifiers
                'note_guid': getattr(linked_note, 'guid', None),
                'card_ord': getattr(card, 'ord', None),
                # helpful extras
                'note_id': linked_note.id,
                'card_id': card.id,
            }
            return info
        except:
            # Minimal fallback with what we have
            return {'title': link_text, 'deck': '', 'card_id': getattr(card, 'id', None)}

    def setup_editor_button(self, buttons, editor):
        """Add link button to editor"""

        def on_click(editor_instance):
            self.on_link_button_clicked(editor_instance)
        button = editor.addButton(icon=None, cmd='card_linker', func=on_click, tip=get_text('link_button_tip'), label=get_text('link_button_label'))
        buttons.append(button)
        return buttons

    def on_link_button_clicked(self, editor):
        """Handle link button click"""
        if not editor.note:
            showInfo(get_text('select_card_first'))
            return
        if not self.check_field_exists(editor.note):
            return
        self.show_link_dialog(editor)

    def create_default_note_type(self):
        """Create default note type with LinkedCards field"""
        try:
            model = mw.col.models.new(get_text('default_note_type_name'))
            front_field = mw.col.models.newField(get_text('front_field_name'))
            mw.col.models.addField(model, front_field)
            back_field = mw.col.models.newField(get_text('back_field_name'))
            mw.col.models.addField(model, back_field)
            linked_cards_field = mw.col.models.newField(self.linked_cards_field)
            mw.col.models.addField(model, linked_cards_field)
            template = mw.col.models.newTemplate(get_text('card_template_name'))
            template['qfmt'] = get_text('front_template')
            template['afmt'] = get_text('back_template')
            mw.col.models.addTemplate(model, template)
            mw.col.models.add(model)
            mw.col.models.save(model)
            return model
        except Exception as e:
            showInfo(get_text('create_note_type_failed').format(str(e)))
            return None

    def add_linked_cards_field_to_model(self, model):
        """Add LinkedCards field to existing model"""
        try:
            linked_cards_field = mw.col.models.newField(self.linked_cards_field)
            mw.col.models.addField(model, linked_cards_field)
            mw.col.models.save(model)
        except Exception as e:
            pass

    def check_field_exists(self, note):
        """Check if LinkedCards field exists and offer solutions"""
        try:
            _ = note[self.linked_cards_field]
            return True
        except KeyError:
            return self.handle_missing_field(note)

    def handle_missing_field(self, note):
        """Handle missing LinkedCards field with smart suggestions"""
        note_type_name = get_text('default_note_type_name')
        existing_models = mw.col.models.all()
        ankiNexus_model = None
        for model in existing_models:
            if model['name'] == note_type_name:
                field_names = [field['name'] for field in model['flds']]
                if self.linked_cards_field in field_names:
                    ankiNexus_model = model
                    break
        if ankiNexus_model:
            return self.suggest_switch_template(ankiNexus_model)
        else:
            return self.suggest_create_template()

    def suggest_switch_template(self, ankiNexus_model):
        """Suggest switching to AnkiNexus template"""
        from aqt.utils import askUser
        message = get_text('switch_template_suggestion').format(ankiNexus_model['name'], self.linked_cards_field)
        if askUser(message):
            showInfo(get_text('manual_switch_instructions').format(ankiNexus_model['name'], ankiNexus_model['name']))
            return False
        return False

    def suggest_create_template(self):
        """Suggest creating AnkiNexus template"""
        from aqt.utils import askUser
        message = get_text('create_template_suggestion').format(self.linked_cards_field)
        if askUser(message):
            model = self.create_default_note_type()
            if model:
                showInfo(get_text('template_created_manual_switch').format(model['name'], model['name']))
                return False
            return False
        else:
            showInfo(get_text('field_missing').format(self.linked_cards_field, self.linked_cards_field))
            return False

    def show_link_dialog(self, editor):
        """Show link dialog"""
        dialog = LinkDialog(editor, self)
        dialog.exec()

    def search_cards(self, query):
        """Search cards"""
        try:
            card_ids = mw.col.findCards(query)
            cards = []
            for card_id in card_ids[:30]:
                card = mw.col.getCard(card_id)
                note = card.note()
                raw_question = note.fields[0] if note.fields else ''
                clean_question = self.clean_card_title_for_search(raw_question)
                cards.append({
                    'id': card_id,
                    'note_id': note.id,
                    'note_guid': getattr(note, 'guid', None),
                    'card_ord': getattr(card, 'ord', None),
                    'question': clean_question[:80],
                    'deck': mw.col.decks.name(card.did)
                })
            return cards
        except:
            return []

    def clean_card_title_for_search(self, title):
        """Clean card title for search results"""
        import re
        clean_title = re.sub('<[^>]+>', '', title)
        clean_title = re.sub('\\s+', ' ', clean_title).strip()
        return clean_title

    def create_new_card(self, current_note, front, back):
        """Create new card"""
        try:
            model = current_note.model()
            new_note = Note(mw.col, model)
            new_note.fields[0] = front
            if len(new_note.fields) > 1:
                new_note.fields[1] = back
            current_card = mw.reviewer.card if mw.reviewer.card else None
            deck_id = current_card.did if current_card else mw.col.conf['curDeck']
            new_note.model()['did'] = deck_id
            mw.col.addNote(new_note)
            mw.col.save()
            new_cards = new_note.cards()
            return new_cards[0].id if new_cards else None
        except Exception as e:
            showInfo(get_text('create_failed').format(str(e)))
            return None

    def insert_link(self, editor, link_text, card_id):
        """Insert link in editor - only store JSON data, no visual link display"""
        pass

    def add_link_to_note(self, note, card_id, link_text):
        """Add link to note. Store stable identifiers (note_guid + card_ord), keep card_id for compatibility."""
        try:
            linked_cards = self.get_linked_cards(note)
            card = mw.col.getCard(card_id)
            if not card:
                showInfo(get_text('error_card_not_found').format(card_id))
                return False
            # duplicate check via stable matching
            if any((self.link_matches_card(link, card) for link in linked_cards)):
                showInfo(get_text('error_card_already_linked'))
                return True
            # build and append link info
            link_info = self.build_link_info(card, link_text)
            linked_cards.append(link_info)
            success = self.save_linked_cards(note, linked_cards)
            if not success:
                showInfo(get_text('error_save_link_failed'))
                return False
            return True
        except Exception as e:
            showInfo(get_text('save_link_failed').format(str(e)))
            return False

    def get_linked_cards(self, note):
        """Get linked cards. If legacy links (only card_id) are found and resolvable, upgrade them to include note_guid/card_ord."""
        try:
            field_content = note[self.linked_cards_field] or '[]'
            links = json.loads(field_content)
            if isinstance(links, list):
                upgraded = False
                for link in links:
                    try:
                        if isinstance(link, dict) and (link.get('note_guid') is None or link.get('card_ord') is None):
                            cid = link.get('card_id')
                            if cid:
                                card = None
                                try:
                                    card = mw.col.getCard(cid)
                                except:
                                    card = None
                                if card:
                                    ln = card.note()
                                    link['note_guid'] = getattr(ln, 'guid', None)
                                    link['card_ord'] = getattr(card, 'ord', None)
                                    link['note_id'] = ln.id
                                    if not link.get('deck'):
                                        link['deck'] = mw.col.decks.name(card.did)
                                    upgraded = True
                    except:
                        continue
                if upgraded:
                    # Persist the upgraded links for future stability
                    self.save_linked_cards(note, links)
            return links if isinstance(links, list) else []
        except:
            return []

    def save_linked_cards(self, note, linked_cards):
        """Save linked cards"""
        try:
            json_data = json.dumps(linked_cards, ensure_ascii=False)
            print(get_text('debug_save_link_data').format(json_data))
            note[self.linked_cards_field] = json_data
            if note.id != 0:
                mw.col.updateNote(note)
                mw.col.save()
                print(get_text('debug_save_success').format(self.linked_cards_field))
            return True
        except Exception as e:
            error_msg = get_text('save_failed').format(str(e))
            print(get_text('debug_save_failed').format(error_msg))
            showInfo(error_msg)
            return False

