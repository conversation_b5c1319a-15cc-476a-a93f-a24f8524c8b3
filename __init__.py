"""
Card Linker Plugin for <PERSON><PERSON>ows linking related cards together to solve card fragmentation
"""
from aqt import mw, gui_hooks
from aqt.qt import *
from aqt.utils import showInfo
from .components.CardLinker import CardLinker
from .lang import get_text

try:
    from PyQt6.QtCore import Qt
    from PyQt6.QtWidgets import QDialog
    USER_ROLE = Qt.ItemDataRole.UserRole
    DIALOG_ACCEPTED = QDialog.DialogCode.Accepted
except:
    try:
        from PyQt5.QtCore import Qt
        from PyQt5.QtWidgets import QDialog
        USER_ROLE = Qt.UserRole
        DIALOG_ACCEPTED = QDialog.Accepted
    except:
        USER_ROLE = 256
        DIALOG_ACCEPTED = 1

card_linker = CardLinker()

def setup_editor_buttons(buttons, editor):
    return card_linker.setup_editor_button(buttons, editor)
gui_hooks.editor_did_init_buttons.append(setup_editor_buttons)

def add_linked_cards_to_review(html, card, context):
    """Display linked cards during review - only on answer side, simplified interaction"""
    if context != 'reviewAnswer':
        return html
    try:
        note = card.note()
        linked_cards = card_linker.get_linked_cards(note)
        if linked_cards:
            css = '''
            <style>
            .linked-cards-container {
                border: 2px solid #2196f3;
                border-radius: 8px;
                padding: 10px;
                margin: 10px 0;
                background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            }
            .linked-cards-wrapper {
                margin-top: 8px;
            }
            .linked-card-item {
                display: block;
                padding: 6px 10px;
                margin: 3px 0;
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 6px;
                color: #333;
                cursor: pointer;
                font-size: 12px;
                transition: all 0.2s ease;
                position: relative;
                box-shadow: 0 1px 2px rgba(0,0,0,0.1);
            }
            .linked-card-item:hover {
                background-color: #f5f5f5;
                border-color: #2196f3;
                transform: translateX(3px);
                box-shadow: 0 2px 6px rgba(0,0,0,0.15);
            }
            .knowledge-point-status {
                float: right;
                font-size: 14px;
                margin-left: 10px;
            }

            /* Detailed status colors */
            .status-completed { color: #4caf50; }
            .status-new { color: #2196f3; }
            .status-reset { color: #ff5722; }
            .status-learning { color: #ff9800; }
            .status-day-learning { color: #ffc107; }
            .status-overdue { color: #f44336; }
            .status-due-today { color: #ff9800; }
            .status-future { color: #9e9e9e; }
            .status-suspended { color: #795548; }
            .status-buried { color: #607d8b; }
            .status-unknown { color: #9e9e9e; }
            .status-error { color: #f44336; }

            .linked-cards-title {
                font-weight: bold;
                text-align: center;
                margin-bottom: 8px;
                color: #1976d2;
                font-size: 14px;
            }
            .linked-cards-tip {
                font-size: 11px;
                color: #666;
                text-align: center;
                margin-top: 6px;
                font-style: italic;
            }
            </style>
            '''
            links_html = '<div class="linked-cards-container">'
            links_html += f"""<div class="linked-cards-title">{get_text('related_knowledge')}</div>"""
            links_html += '<div class="linked-cards-wrapper">'
            for link in linked_cards:
                try:
                    # Resolve link to current card (supports legacy card_id and GUID+ord)
                    linked_card = card_linker.resolve_link_to_card(link)
                    if linked_card:
                        status_info = get_card_status_info(linked_card)
                        status_icon = status_info['icon']
                        status_class = status_info['class']
                        status_description = status_info['description']
                        is_actionable = status_info['is_actionable']

                        click_action = f"pycmd('linked_card:{linked_card.id}:{str(is_actionable).lower()}')"
                        safe_title = link['title'].replace('"', '&quot;').replace("'", '&#39;')
                        safe_deck = link.get('deck', mw.col.decks.name(linked_card.did)).replace('"', '&quot;').replace("'", '&#39;')
                        tooltip = f"{safe_title} ({get_text('deck_label')}: {safe_deck}) - {status_description}"
                        links_html += f'<div class="linked-card-item" onclick="{click_action}" title="{tooltip}">📚 {safe_title}<span class="knowledge-point-status {status_class}">{status_icon}</span></div>'
                    else:
                        safe_title = link.get('title', '').replace('"', '&quot;').replace("'", '&#39;')
                        deleted_text = get_text('card_status_deleted')
                        links_html += f'<div class="linked-card-item" style="opacity: 0.5; cursor: not-allowed;" title="{safe_title} ({deleted_text})">📚 {safe_title} ❌</div>'
                except Exception as e:
                    safe_title = link.get('title', get_text('card_status_unknown')).replace('"', '&quot;').replace("'", '&#39;')
                    error_text = get_text('card_status_load_error')
                    links_html += f'<div class="linked-card-item" style="opacity: 0.5; cursor: not-allowed;" title="{safe_title} ({error_text})">📚 {safe_title} ⚠️</div>'
                    continue
            links_html += '</div>'
            links_html += f"""<div class="linked-cards-tip">{get_text('review_status_tip')}</div>"""
            links_html += f"""<div class="linked-cards-tip">{get_text('deck_switch_notice')}</div>"""
            links_html += '</div>'
            html = css + html + links_html
    except:
        pass
    return html

def get_card_status_info(card):
    """Get detailed card status information with icon and description"""
    try:
        # Queue values: 0=new, 1=learning, 2=review, 3=day learning, -1=suspended, -2=user buried, -3=sched buried

        # Check if card is suspended
        if card.queue == -1:
            return {
                'icon': '⏸️',
                'status': 'suspended',
                'class': 'status-suspended',
                'description': get_text('card_status_suspended'),
                'is_actionable': True
            }

        # Check if card is buried (user or scheduler)
        if card.queue == -2:
            return {
                'icon': '📦',
                'status': 'user_buried',
                'class': 'status-buried',
                'description': get_text('card_status_user_buried'),
                'is_actionable': True
            }

        if card.queue == -3:
            return {
                'icon': '📦',
                'status': 'sched_buried',
                'class': 'status-buried',
                'description': get_text('card_status_sched_buried'),
                'is_actionable': True
            }

        # Check if card is new
        if card.queue == 0:
            # Check if this was a reset card (has review history)
            try:
                import time
                reviews = mw.col.db.list('select id from revlog where cid = ?', card.id)
                if len(reviews) > 0:
                    return {
                        'icon': '🔄',
                        'status': 'reset',
                        'class': 'status-reset',
                        'description': get_text('card_status_reset'),
                        'is_actionable': True
                    }
                else:
                    return {
                        'icon': '🆕',
                        'status': 'new',
                        'class': 'status-new',
                        'description': get_text('card_status_new'),
                        'is_actionable': True
                    }
            except:
                return {
                    'icon': '🆕',
                    'status': 'new',
                    'class': 'status-new',
                    'description': get_text('card_status_new'),
                    'is_actionable': True
                }

        # Check if card is in learning
        if card.queue == 1:
            return {
                'icon': '📖',
                'status': 'learning',
                'class': 'status-learning',
                'description': get_text('card_status_learning'),
                'is_actionable': True
            }

        # Check if card is in day learning
        if card.queue == 3:
            return {
                'icon': '📅',
                'status': 'day_learning',
                'class': 'status-day-learning',
                'description': get_text('card_status_day_learning'),
                'is_actionable': True
            }

        # Check if card is in review queue
        if card.queue == 2:
            try:
                # Get current day number
                current_day = mw.col.sched.today

                # Check if card is due today or overdue
                if card.due <= current_day:
                    if card.due < current_day:
                        return {
                            'icon': '🔴',
                            'status': 'overdue',
                            'class': 'status-overdue',
                            'description': get_text('card_status_overdue'),
                            'is_actionable': True
                        }
                    else:
                        return {
                            'icon': '⏳',
                            'status': 'due_today',
                            'class': 'status-due-today',
                            'description': get_text('card_status_due_today'),
                            'is_actionable': True
                        }
                else:
                    # Card is not due yet - check if reviewed today
                    try:
                        import time
                        today_start = int(time.time()) - int(time.time()) % 86400
                    except:
                        try:
                            today_start = mw.col.sched.day_cutoff - 86400
                        except:
                            import time
                            today_start = int(time.time()) - 86400

                    reviews = mw.col.db.list('select id from revlog where cid = ? and id > ?', card.id, today_start * 1000)
                    if len(reviews) > 0:
                        return {
                            'icon': '✅',
                            'status': 'completed_today',
                            'class': 'status-completed',
                            'description': get_text('card_status_completed_today'),
                            'is_actionable': False
                        }
                    else:
                        return {
                            'icon': '💤',
                            'status': 'future',
                            'class': 'status-future',
                            'description': get_text('card_status_future'),
                            'is_actionable': False
                        }
            except:
                return {
                    'icon': '❓',
                    'status': 'unknown_review',
                    'class': 'status-unknown',
                    'description': get_text('card_status_unknown_review'),
                    'is_actionable': False
                }

        # Unknown state
        return {
            'icon': '❓',
            'status': 'unknown',
            'class': 'status-unknown',
            'description': get_text('card_status_unknown'),
            'is_actionable': False
        }

    except Exception as e:
        return {
            'icon': '⚠️',
            'status': 'error',
            'class': 'status-error',
            'description': get_text('card_status_error'),
            'is_actionable': False
        }

def check_card_reviewed_today(card):
    """Check if card has been reviewed today and is in a completed state - compatible version"""
    status_info = get_card_status_info(card)
    return status_info['status'] == 'completed_today'

def is_card_in_current_deck(card):
    """Check if card is in the current review deck"""
    try:
        if not mw.reviewer or not mw.reviewer.card:
            return False
        current_deck_id = mw.reviewer.card.did
        target_deck_id = card.did
        if current_deck_id == target_deck_id:
            return True
        current_deck_name = mw.col.decks.name(current_deck_id)
        target_deck_name = mw.col.decks.name(target_deck_id)
        if target_deck_name.startswith(current_deck_name + '::'):
            return True
        return False
    except Exception as e:
        return False

def handle_linked_card_click(cmd):
    """Handle linked card click"""
    try:
        if cmd.startswith('linked_card:'):
            parts = cmd.split(':')
            if len(parts) < 3:
                showInfo(f'Command format error: {cmd}')
                return
            card_id = int(parts[1])
            is_actionable = parts[2] == 'true'
            target_card = mw.col.getCard(card_id)
            if not target_card:
                showInfo(get_text('card_not_found'))
                return

            # Try to review actionable cards directly, show preview for non-actionable cards
            if is_actionable:
                # Try to switch to the target card for immediate review
                if try_switch_to_card(card_id):
                    return
                else:
                    # If switching fails, fall back to preview
                    show_card_preview(card_id)
            else:
                # For non-actionable cards (completed, future), show preview
                show_card_preview(card_id)
    except Exception as e:
        error_msg = f'Click handling failed: {str(e)}'
        showInfo(error_msg)

def try_switch_to_card(card_id):
    """Try to switch to target card for immediate review"""
    try:
        # Check if we're in review mode
        if not mw.reviewer or not mw.reviewer.card:
            return False

        target_card = mw.col.getCard(card_id)
        if not target_card:
            return False

        # Check if target card is the current card
        if mw.reviewer.card.id == card_id:
            # If it's the same card, just refresh the display
            mw.reviewer._showQuestion()
            return True

        # Handle suspended/buried cards
        if target_card.queue < 0:
            if not handle_suspended_card(target_card):
                return False
            # Reload card after unsuspending
            target_card = mw.col.getCard(card_id)

        # Try to make the card immediately reviewable
        success = make_card_reviewable(target_card)
        if success:
            # Reset scheduler and get next card
            try:
                mw.col.sched.reset()
                mw.reviewer.nextCard()
                return True
            except:
                return False

        return False
    except Exception:
        return False

def make_card_reviewable(card):
    """Make a card immediately reviewable by setting it to learning state"""
    try:
        import time
        now = int(time.time())

        # Set card to learning state with immediate due time
        card.type = 1    # Learning card
        card.queue = 1   # Learning queue
        card.due = now   # Due immediately

        # Update database
        mw.col.db.execute(
            'update cards set type=?, queue=?, due=? where id=?',
            card.type, card.queue, card.due, card.id
        )
        mw.col.save()
        return True
    except Exception:
        return False

def show_card_preview(card_id):
    """Show card preview"""
    try:
        card = mw.col.getCard(card_id)
        if not card:
            showInfo(get_text('card_not_found'))
            return
        from aqt.browser import Browser
        from aqt.qt import QTimer
        browser = Browser(mw)
        browser.form.searchEdit.lineEdit().setText(f'cid:{card_id}')
        browser.onSearchActivated()
        browser.show()

        def auto_preview():
            try:
                if hasattr(browser, 'table') and browser.table.len_selection() > 0:
                    if hasattr(browser.form, 'actionPreview'):
                        browser.form.actionPreview.trigger()
                    elif hasattr(browser, 'onTogglePreview'):
                        browser.onTogglePreview()
                    elif hasattr(browser, '_on_preview_clicked'):
                        browser._on_preview_clicked()
                    else:
                        showInfo(get_text('manual_preview'))
                else:
                    showInfo(get_text('manual_preview'))
            except:
                showInfo(get_text('manual_preview'))
        QTimer.singleShot(1000, auto_preview)
    except Exception as e:
        showInfo(get_text('preview_failed'))



def handle_suspended_card(card):
    """Handle suspended or buried card"""
    from aqt.utils import askUser
    try:
        if card.queue == -1:
            message = get_text('unsuspend_card_question')
            if askUser(message):
                mw.col.sched.unsuspendCards([card.id])
                mw.col.save()
                showInfo(get_text('card_unsuspended'))
                return True
            else:
                return False
        elif card.queue in (-2, -3):
            message = get_text('unbury_card_question')
            if askUser(message):
                mw.col.sched.unbury_cards([card.id])
                mw.col.save()
                showInfo(get_text('card_unburied'))
                return True
        else:
            message = get_text('restore_card_question')
            if askUser(message):
                card.queue = 0
                card.type = 0
                mw.col.updateCard(card)
                mw.col.save()
                showInfo(get_text('card_restored'))
                return True
            else:
                return False
        return False
    except Exception as e:
        showInfo(get_text('unsuspend_failed').format(str(e)))
        return False

def get_current_time():
    """Get current timestamp - compatible with different Anki versions"""
    try:
        import time
        return int(time.time())
    except:
        try:
            return mw.col.sched.intTime()
        except:
            import time
            return int(time.time())

def handle_card_linker_open_link(cmd):
    """Handle card_linker:open_link: command with stable identifiers"""
    try:
        # Extract the link identifier from the command
        # Format: card_linker:open_link:guid:GUID:ORD or card_linker:open_link:id:CARD_ID
        if not cmd.startswith('card_linker:open_link:'):
            return

        link_id = cmd[len('card_linker:open_link:'):]

        # Parse the link identifier
        if link_id.startswith('guid:'):
            # Format: guid:GUID:ORD
            parts = link_id.split(':')
            if len(parts) >= 3:
                note_guid = parts[1]
                try:
                    card_ord = int(parts[2])
                except ValueError:
                    showInfo(get_text('invalid_link_format'))
                    return

                # Find the card using note_guid and card_ord
                card = card_linker.resolve_link_to_card({
                    'note_guid': note_guid,
                    'card_ord': card_ord
                })

                if card:
                    # Use the existing linked card click handler
                    status_info = get_card_status_info(card)
                    is_actionable = status_info['is_actionable']
                    handle_linked_card_click(f'linked_card:{card.id}:{str(is_actionable).lower()}')
                else:
                    showInfo(get_text('card_not_found'))
            else:
                showInfo(get_text('invalid_link_format'))

        elif link_id.startswith('id:'):
            # Format: id:CARD_ID (legacy support)
            parts = link_id.split(':')
            if len(parts) >= 2:
                try:
                    card_id = int(parts[1])
                    card = mw.col.getCard(card_id)
                    if card:
                        # Use the existing linked card click handler
                        status_info = get_card_status_info(card)
                        is_actionable = status_info['is_actionable']
                        handle_linked_card_click(f'linked_card:{card_id}:{str(is_actionable).lower()}')
                    else:
                        showInfo(get_text('card_not_found'))
                except ValueError:
                    showInfo(get_text('invalid_link_format'))
            else:
                showInfo(get_text('invalid_link_format'))
        else:
            showInfo(get_text('invalid_link_format'))

    except Exception as e:
        error_msg = f'Link handling failed: {str(e)}'
        showInfo(error_msg)

def setup_link_handler():
    """Setup link handler"""
    if hasattr(mw.reviewer, '_linkHandler'):
        original_handler = mw.reviewer._linkHandler

        def new_handler(url):
            if url.startswith('linked_card:'):
                handle_linked_card_click(url)
            elif url.startswith('card_linker:open_link:'):
                handle_card_linker_open_link(url)
            elif original_handler:
                original_handler(url)
        mw.reviewer._linkHandler = new_handler

def on_reviewer_init():
    """Setup handler when reviewer initializes"""
    setup_link_handler()

gui_hooks.reviewer_did_init.append(lambda x: setup_link_handler())
gui_hooks.card_will_show.append(add_linked_cards_to_review)