# AnkiNexus - 知识链接器

[![Anki版本](https://img.shields.io/badge/Anki-2.1.50+-blue.svg)](https://apps.ankiweb.net/)
[![许可证](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![语言](https://img.shields.io/badge/Language-中文%20%7C%20English-orange.svg)](README.md)

一个强大的Anki插件，通过创建复合卡片和链接相关知识点来解决卡片碎片化问题。具有智能模板创建、智能复习体验和无缝双语支持等特性。

## ✨ 主要特性

### 🔗 知识点链接
- **智能链接创建**：轻松将相关卡片链接在一起，构建知识网络
- **实时搜索**：快速搜索现有卡片并创建链接
- **批量管理**：支持多选、批量添加和移除链接
- **自动保存**：链接创建后立即保存，无需额外确认

### 🧠 智能复习体验
- **相关知识点显示**：复习时自动显示相关的链接卡片
- **复习状态指示**：绿色✅表示今日已复习，橙色⏳表示待复习
- **智能跳转**：点击链接卡片可直接跳转复习或预览
- **暂停卡片处理**：自动处理暂停/搁置的卡片，提供恢复选项

### 📝 快速卡片创建
- **内置创建器**：在链接对话框中直接创建新卡片
- **自动链接**：新创建的卡片自动添加为链接
- **模板兼容**：使用当前笔记类型的模板创建新卡片

### 🎨 用户友好界面
- **直观设计**：清晰的搜索和选择界面
- **实时反馈**：操作状态实时显示
- **双语支持**：完整的中英文界面支持
- **响应式布局**：适配不同屏幕尺寸

## 🚀 快速开始

### 安装方法

1. **从AnkiWeb安装**（推荐）
   - 打开Anki，进入 `工具` > `插件`
   - 点击 `获取插件...`
   - 输入插件代码：`[插件代码]`
   - 重启Anki

2. **手动安装**
   - 下载最新版本的插件文件
   - 解压到Anki插件目录：`%APPDATA%\Anki2\addons21\`
   - 重启Anki

### 首次使用

1. **创建或选择笔记类型**
   - 插件会自动检测是否存在兼容的笔记类型
   - 如果没有，会提示创建"AnkiNexus - 知识链接器"模板
   - 该模板包含必需的`LinkedCards`字段

2. **开始创建链接**
   - 在编辑器中点击🔗按钮
   - 搜索相关卡片或创建新卡片
   - 双击或点击添加按钮创建链接

## 📖 使用指南

### 创建知识链接

1. **打开链接对话框**
   - 在卡片编辑器中点击🔗按钮
   - 确保当前笔记类型包含`LinkedCards`字段

2. **搜索现有卡片**
   - 在搜索框中输入关键词
   - 浏览搜索结果
   - 双击卡片或点击"添加选中卡片"按钮

3. **创建新卡片**
   - 点击"创建新卡片"按钮
   - 填写正面和背面内容
   - 新卡片会自动添加为链接

4. **管理链接**
   - 查看已选择的卡片列表
   - 移除不需要的链接
   - 清空所有链接

### 复习体验

在复习过程中，相关的知识点会显示在答案下方：

- **🧠 相关知识点**：显示所有链接的卡片
- **状态指示器**：
  - ✅ 绿色：今日已复习
  - ⏳ 橙色：待复习
- **交互功能**：
  - 点击链接卡片可跳转复习或预览
  - 自动处理暂停/搁置的卡片

### 高级功能

#### 智能模板管理
- 自动检测现有的兼容模板
- 提供模板创建和切换建议
- 支持手动模板切换指导

#### 批量操作
- 多选卡片进行批量链接
- 批量移除不需要的链接
- 一键清空所有链接

#### 跨牌组支持
- 支持链接不同牌组的卡片
- 智能处理牌组切换
- 预览模式支持跨牌组卡片

## ⚙️ 配置选项

插件支持以下配置选项（在`config.json`中）：

```json
{
    "language": "auto",
    "auto_create_template": true,
    "max_search_results": 30,
    "enable_preview_mode": true
}
```

- `language`：界面语言（"auto"、"zh"、"en"）
- `auto_create_template`：是否自动创建模板
- `max_search_results`：最大搜索结果数量
- `enable_preview_mode`：是否启用预览模式

## 🔧 技术特性

### 兼容性
- **Anki版本**：2.1.50+
- **Python版本**：3.8+
- **Qt版本**：PyQt5/PyQt6自适应
- **操作系统**：Windows、macOS、Linux

### 数据存储
- 使用JSON格式存储链接数据
- 存储在`LinkedCards`字段中
- 支持数据导入导出
- 兼容Anki同步机制

### 性能优化
- 智能搜索算法
- 延迟加载机制
- 内存使用优化
- 响应式界面更新

## 🤝 贡献指南

欢迎贡献代码、报告问题或提出建议！

### 开发环境设置

1. 克隆仓库
```bash
git clone https://github.com/your-username/AnkiNexus.git
cd AnkiNexus
```

2. 创建开发环境
```bash
# 确保已安装Anki 2.1.50+
# 将插件文件夹链接到Anki插件目录
```

3. 运行测试
```bash
# 在Anki中测试插件功能
# 检查控制台输出
```

### 提交规范

- 使用清晰的提交信息
- 遵循现有代码风格
- 添加必要的测试
- 更新相关文档

## 📝 更新日志

### v1.1.0 (2024-01-XX)
- ✨ 新增批量链接管理功能
- 🎨 优化用户界面设计
- 🐛 修复暂停卡片处理问题
- 🌐 完善双语支持

### v1.0.0 (2024-01-XX)
- 🎉 首次发布
- 🔗 基础链接功能
- 🧠 智能复习体验
- 📝 快速卡片创建

## 📄 许可证

本项目采用MIT许可证 - 查看[LICENSE](LICENSE)文件了解详情。

## 🙏 致谢

- 感谢Anki开发团队提供优秀的平台
- 感谢社区用户的反馈和建议
- 感谢所有贡献者的努力

## 📞 联系方式

- **GitHub Issues**：[报告问题](https://github.com/your-username/AnkiNexus/issues)
- **讨论区**：[GitHub Discussions](https://github.com/your-username/AnkiNexus/discussions)
- **邮箱**：<EMAIL>

---

**让知识连接，让学习更高效！** 🚀
